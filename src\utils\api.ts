const API_BASE_URL = '/api';

export interface LoginResponse {
  token: string;
  imeiCodes: string[];
}

export interface RegisterRequest {
  username: string;
  password: string;
  imeiCodes?: string[];
}

export interface UpdateImeiRequest {
  token: string;
  imeiCodes: string[];
}

export class ApiClient {
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;
    
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || `HTTP ${response.status}`);
    }

    return response.json();
  }

  async login(username: string, password: string): Promise<LoginResponse> {
    return this.request<LoginResponse>('/auth/login', {
      method: 'POST',
      body: JSON.stringify({ username, password }),
    });
  }

  async register(data: RegisterRequest): Promise<{ success: boolean }> {
    return this.request<{ success: boolean }>('/auth/register', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updateImeiCodes(data: UpdateImeiRequest): Promise<{ success: boolean }> {
    return this.request<{ success: boolean }>('/update-imei', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async healthCheck(): Promise<{ status: string; timestamp: string }> {
    return this.request<{ status: string; timestamp: string }>('/health');
  }
}

export const apiClient = new ApiClient();
